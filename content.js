let liElements
let liLength
let index
// let currentScrollPosition = 0
let work = true

async function main() {
  updateLi()
  while (index + 1 !== liLength && work) {
    try {
      await pause(100)
      updateLi()
      let currentLi = liElements[index]

      // Проверяем, нужно ли пропустить эту конференцию
      if (itsGM(currentLi) && await shouldSkipConference(currentLi)) {
        console.log('Пропускаем конференцию с защищенным ником или названием')
        clickOnElement(index + 1)
        await pause(100)
        continue
      }

      if (itsGM(currentLi)) {
        await getAndDeleteAllMembers()
        await randomDelay() // Добавляем случайную задержку между удалениями
      }
      await pause(150)
      clickOnElement(index + 1)
      await pause(100)
      if (itsGM(currentLi)) await leave(currentLi)
    } catch (error) {
      console.error('Ошибка в основном цикле:', error)
      work = false
      alert('Произошла ошибка в основном цикле. Скрипт остановлен.')
      break
    }
  }
}

async function leave(el, i) {
  try {
    if (el) el.querySelector('[class^="closeButton_"]').click()
    if (i) liElements[i].querySelector('[class^="closeButton_"]').click()
    await pause(150)

    // Нажимаем на чекбокс "Leave without notifying other members"
    try {
      let checkbox = document.querySelector('label[data-toggleable-component="checkbox"] input[type="checkbox"]')
      if (checkbox && !checkbox.checked) {
        checkbox.click()
        await pause(100)
      }
    } catch (checkboxError) {
      console.log('Чекбокс "Leave without notifying other members" не найден или уже отмечен')
    }

    document.querySelector('button[type="submit"]').click()
  } catch (error) {
    console.error('Ошибка при выходе из конференции:', error)
    work = false
    alert('Ошибка при выходе из конференции. Скрипт остановлен.')
    throw error
  }
}

function itsGM(el, i) {
  try {
    if (el) return el.querySelector('a').getAttribute('aria-label').includes('group message')
    if (i) return liElements[i].querySelector('a').getAttribute('aria-label').includes('group message')
  } catch (error) {
    console.error('Ошибка при проверке типа сообщения:', error)
    return false
  }
}

async function shouldSkipConference(el, i) {
  let element = el || liElements[i]
  if (!element) return false

  try {
    // Проверяем название конференции на наличие слова "Granas"
    let ariaLabel = element.querySelector('a').getAttribute('aria-label')
    if (ariaLabel && ariaLabel.toLowerCase().includes('granas')) {
      console.log(`Пропускаем конференцию с названием содержащим "Granas": ${ariaLabel}`)
      return true
    }

    // Получаем всех участников конференции
    let members = document.querySelectorAll('[class^="member_"]')

    // Защищенные ники
    let protectedNames = ['melktrupp', 'melk trupp']

    // Ники для точного совпадения (если НЕТ точного совпадения, то пропускаем)
    let exactMatchNames = ['overgear', 'og', 'cc']
    let hasExactMatch = false

    // Проверяем каждого участника
    for (let member of members) {
      try {
        // Получаем имя пользователя из различных возможных элементов
        let usernameElement = member.querySelector('[class*="username"]') ||
          member.querySelector('[class*="name"]') ||
          member.querySelector('[class*="displayName"]') ||
          member.querySelector('[class*="nick"]') ||
          member.querySelector('span') ||
          member.querySelector('div')

        if (usernameElement) {
          let username = usernameElement.textContent.toLowerCase().trim()

          // Проверяем, содержит ли имя пользователя один из защищенных ников
          if (protectedNames.some(name => username.includes(name))) {
            console.log(`Найден защищенный пользователь: ${usernameElement.textContent}`)
            return true
          }

          // Проверяем точное совпадение с нужными никами
          if (exactMatchNames.includes(username)) {
            hasExactMatch = true
          }
        }

        // Дополнительная проверка: ищем по aria-label или title
        let memberAriaLabel = member.getAttribute('aria-label') || member.getAttribute('title') || ''
        if (memberAriaLabel) {
          let labelText = memberAriaLabel.toLowerCase().trim()
          if (protectedNames.some(name => labelText.includes(name))) {
            console.log(`Найден защищенный пользователь в aria-label: ${memberAriaLabel}`)
            return true
          }

          // Проверяем точное совпадение в aria-label
          if (exactMatchNames.includes(labelText)) {
            hasExactMatch = true
          }
        }

      } catch (error) {
        // Игнорируем ошибки при получении имени пользователя
        continue
      }
    }

    // Если нет точного совпадения с overgear, og или cc, пропускаем конференцию
    if (!hasExactMatch) {
      console.log('Пропускаем конференцию: нет точного совпадения с Overgear, OG или CC')
      return true
    }

    return false
  } catch (error) {
    console.error('Ошибка при проверке конференции:', error)
    // Прерываем выполнение скрипта при критической ошибке
    work = false
    alert('Произошла ошибка при проверке конференции. Скрипт остановлен.')
    throw error
  }
}

function clickOnElement(i) {
  try {
    if (i + 2 === liLength) {
      alert('Дошли до конца, если удалило не все - перезапустите')
      work = false
    }
    liElements[i].querySelector('[class^="name__"]').click()
  } catch (error) {
    console.error('Ошибка при клике на элемент:', error)
    work = false
    alert('Ошибка при клике на элемент. Скрипт остановлен.')
    throw error
  }
}

function updateLi() {
  try {
    liElements = document.querySelectorAll('li[role="listitem"]')
    index = Array.from(liElements)
      .flatMap((y) => Array.from(y.children))
      .findIndex((element) => element.classList.value.match(/selected\w+/))
    liLength = Array.from(liElements).length
  } catch (error) {
    console.error('Ошибка при обновлении списка элементов:', error)
    work = false
    alert('Ошибка при обновлении списка элементов. Скрипт остановлен.')
    throw error
  }
}

async function getAndDeleteAllMembers() {
  try {
    let members = document.querySelectorAll('[class^="member_"]')
    for (let el of members) {
      //await deleteMember(el)
    }
  } catch (error) {
    console.error('Ошибка при удалении участников:', error)
    work = false
    alert('Ошибка при удалении участников. Скрипт остановлен.')
    throw error
  }
}

// async function deleteMember(el) {
//   try {
//     let event = new MouseEvent('contextmenu', {
//       bubbles: true,
//       cancelable: true,
//       view: window,
//     })

//     el.dispatchEvent(event)
//     await pause(150)
//     let menu = document.querySelector('#user-context-remove')
//     if (menu) menu.click()
//   } catch (error) {
//     console.error('Ошибка при удалении участника:', error)
//     // Не прерываем выполнение для отдельного участника
//   }
// }

async function pause(milliseconds) {
  return new Promise((resolve) => setTimeout(resolve, milliseconds))
}

// Функция для случайной задержки между удалениями
async function randomDelay() {
  const minDelay = 1000 // 10 секунд
  const maxDelay = 3000 // 20 секунд
  const delay = Math.floor(Math.random() * (maxDelay - minDelay + 1)) + minDelay
  console.log(`Ожидание ${delay / 1000} секунд перед следующим действием...`)
  return new Promise((resolve) => setTimeout(resolve, delay))
}

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action === 'start') {
    work = true
    main()
  }
})

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action === 'stop') {
    work = false
  }
})

chrome.runtime.onMessage.addListener(async function (request, sender, sendResponse) {
  if (request.action === 'deletewan') {
    try {
      updateLi()
      let currentLi = liElements[index]

      // Проверяем, нужно ли пропустить эту конференцию
      if (itsGM(currentLi) && await shouldSkipConference(currentLi)) {
        console.log('Пропускаем конференцию с защищенным ником или названием')
        clickOnElement(index + 1)
        await pause(100)
        return
      }

      if (itsGM(currentLi)) {
        await getAndDeleteAllMembers()
        await randomDelay() // Добавляем случайную задержку
      }
      await pause(150)
      clickOnElement(index + 1)
      await pause(100)
      if (itsGM(currentLi)) await leave(currentLi)
    } catch (error) {
      console.error('Ошибка при удалении одной конференции:', error)
      alert('Ошибка при удалении конференции.')
    }
  }
})
